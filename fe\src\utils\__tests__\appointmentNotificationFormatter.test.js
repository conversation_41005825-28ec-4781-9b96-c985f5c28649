/**
 * Tests for appointment notification formatter utility
 */

import {
  formatAppointmentNotificationMessage,
  shouldFilterNotification,
  processNotification,
  processNotifications
} from '../appointmentNotificationFormatter';

describe('appointmentNotificationFormatter', () => {
  describe('formatAppointmentNotificationMessage', () => {
    test('should format process update messages correctly', () => {
      expect(formatAppointmentNotificationMessage('Quy trình được cập nhật thành 2'))
        .toBe('Quy trình được cập nhật thành: Khám sức khỏe cơ bản');
      
      expect(formatAppointmentNotificationMessage('Quy trình được cập nhật thành 5'))
        .toBe('Quy trình được cập nhật thành: Nhập kho');
    });

    test('should format status update messages correctly', () => {
      expect(formatAppointmentNotificationMessage('Trạng thái được cập nhật thành true'))
        .toBe('Trạng thái được cập nhật thành: Chấp nhận');
      
      expect(formatAppointmentNotificationMessage('Trạng thái được cập nhật thành false'))
        .toBe('Trạng thái được cập nhật thành: Không chấp nhận');
    });

    test('should return original message if no pattern matches', () => {
      const originalMessage = 'Bạn có lịch hẹn hiến máu mới';
      expect(formatAppointmentNotificationMessage(originalMessage))
        .toBe(originalMessage);
    });

    test('should handle null/undefined messages', () => {
      expect(formatAppointmentNotificationMessage(null)).toBe(null);
      expect(formatAppointmentNotificationMessage(undefined)).toBe(undefined);
      expect(formatAppointmentNotificationMessage('')).toBe('');
    });
  });

  describe('shouldFilterNotification', () => {
    test('should filter out notes update notifications', () => {
      expect(shouldFilterNotification({ message: 'Ghi chú được cập nhật' })).toBe(true);
      expect(shouldFilterNotification({ message: 'Notes updated' })).toBe(true);
      expect(shouldFilterNotification({ Message: 'note updated' })).toBe(true);
    });

    test('should not filter other notifications', () => {
      expect(shouldFilterNotification({ message: 'Quy trình được cập nhật thành 2' })).toBe(false);
      expect(shouldFilterNotification({ message: 'Bạn có lịch hẹn mới' })).toBe(false);
    });

    test('should handle null/undefined notifications', () => {
      expect(shouldFilterNotification(null)).toBe(false);
      expect(shouldFilterNotification(undefined)).toBe(false);
      expect(shouldFilterNotification({})).toBe(false);
    });
  });

  describe('processNotification', () => {
    test('should return null for filtered notifications', () => {
      const notification = { message: 'Ghi chú được cập nhật' };
      expect(processNotification(notification)).toBe(null);
    });

    test('should format and return non-filtered notifications', () => {
      const notification = { 
        id: 1,
        message: 'Quy trình được cập nhật thành 2',
        isRead: false
      };
      
      const result = processNotification(notification);
      expect(result).not.toBe(null);
      expect(result.message).toBe('Quy trình được cập nhật thành: Khám sức khỏe cơ bản');
      expect(result.Message).toBe('Quy trình được cập nhật thành: Khám sức khỏe cơ bản');
      expect(result.id).toBe(1);
      expect(result.isRead).toBe(false);
    });
  });

  describe('processNotifications', () => {
    test('should process array of notifications correctly', () => {
      const notifications = [
        { id: 1, message: 'Quy trình được cập nhật thành 2' },
        { id: 2, message: 'Ghi chú được cập nhật' }, // Should be filtered out
        { id: 3, message: 'Trạng thái được cập nhật thành true' },
        { id: 4, message: 'Bạn có lịch hẹn mới' }
      ];

      const result = processNotifications(notifications);
      
      expect(result).toHaveLength(3); // One notification filtered out
      expect(result[0].message).toBe('Quy trình được cập nhật thành: Khám sức khỏe cơ bản');
      expect(result[1].message).toBe('Trạng thái được cập nhật thành: Chấp nhận');
      expect(result[2].message).toBe('Bạn có lịch hẹn mới');
    });

    test('should handle empty array', () => {
      expect(processNotifications([])).toEqual([]);
    });

    test('should handle non-array input', () => {
      expect(processNotifications(null)).toEqual([]);
      expect(processNotifications(undefined)).toEqual([]);
      expect(processNotifications('not an array')).toEqual([]);
    });
  });
});

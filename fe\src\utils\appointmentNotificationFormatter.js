/**
 * Utility for formatting appointment update notifications
 * Converts generic backend messages to user-friendly descriptions
 */

// Process step descriptions
const PROCESS_DESCRIPTIONS = {
  1: "Đ<PERSON>ng ký",
  2: "<PERSON><PERSON><PERSON><PERSON> sức khỏe cơ bản", 
  3: "<PERSON><PERSON><PERSON> máu",
  4: "<PERSON><PERSON><PERSON> nghiệm máu",
  5: "<PERSON>hậ<PERSON> kho"
};

// Status descriptions
const STATUS_DESCRIPTIONS = {
  true: "Chấp nhận",
  false: "Không chấp nhận",
  "true": "Chấp nhận",
  "false": "<PERSON>hông chấp nhận"
};

/**
 * Format appointment notification message to be more user-friendly
 * @param {string} message - Original message from backend
 * @returns {string} - Formatted user-friendly message
 */
export const formatAppointmentNotificationMessage = (message) => {
  if (!message) return message;

  // Handle process update messages
  const processUpdateRegex = /Quy trình được cập nhật thành (\d+)/i;
  const processMatch = message.match(processUpdateRegex);
  if (processMatch) {
    const processNumber = parseInt(processMatch[1]);
    const processDescription = PROCESS_DESCRIPTIONS[processNumber] || "Không xác định";
    return `Quy trình được cập nhật thành: ${processDescription}`;
  }

  // Handle status update messages
  const statusUpdateRegex = /Trạng thái được cập nhật thành (true|false)/i;
  const statusMatch = message.match(statusUpdateRegex);
  if (statusMatch) {
    const statusValue = statusMatch[1].toLowerCase();
    const statusDescription = STATUS_DESCRIPTIONS[statusValue] || "Không xác định";
    return `Trạng thái được cập nhật thành: ${statusDescription}`;
  }

  // Return original message if no pattern matches
  return message;
};

/**
 * Check if a notification should be filtered out (hidden from user)
 * @param {Object} notification - Notification object
 * @returns {boolean} - True if notification should be filtered out
 */
export const shouldFilterNotification = (notification) => {
  if (!notification || !notification.message) return false;

  const message = notification.message || notification.Message || "";

  // Filter out "Ghi chú được cập nhật" notifications
  if (message.includes("Ghi chú được cập nhật") || 
      message.includes("Notes updated") ||
      message.includes("note updated")) {
    return true;
  }

  return false;
};

/**
 * Process and format a notification object
 * @param {Object} notification - Raw notification from backend
 * @returns {Object|null} - Formatted notification or null if should be filtered
 */
export const processNotification = (notification) => {
  if (!notification) return null;

  // Check if notification should be filtered out
  if (shouldFilterNotification(notification)) {
    return null;
  }

  // Format the message
  const originalMessage = notification.message || notification.Message || "";
  const formattedMessage = formatAppointmentNotificationMessage(originalMessage);

  // Return formatted notification
  return {
    ...notification,
    message: formattedMessage,
    Message: formattedMessage // Support both camelCase and PascalCase
  };
};

/**
 * Process an array of notifications
 * @param {Array} notifications - Array of raw notifications
 * @returns {Array} - Array of processed notifications (filtered and formatted)
 */
export const processNotifications = (notifications) => {
  if (!Array.isArray(notifications)) return [];

  return notifications
    .map(processNotification)
    .filter(notification => notification !== null);
};

export default {
  formatAppointmentNotificationMessage,
  shouldFilterNotification,
  processNotification,
  processNotifications
};

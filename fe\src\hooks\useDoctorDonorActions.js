import bloodDonationService from "../services/bloodDonationService";
import NotificationService from "../services/notificationService";
import { toast } from "../utils/toastUtils";

/**
 * Custom hook for handling doctor donor management actions (save, update, etc.)
 */
export const useDoctorDonorActions = () => {

  // Save donor info update (Doctor Update Modal)
  const handleSaveUpdate = async (selectedDonor, updateData, currentUser, onSuccess, setShowUpdateModal, setSelectedDonor) => {
    if (!selectedDonor) return;

    try {
      // 1. Update appointment with doctor information
      await bloodDonationService.doctorUpdateAppointment(selectedDonor.id, {
        ...updateData,
        doctorId: currentUser?.id || 4
      });

      // 2. If blood test results are provided (bloodGroup and rhType), update blood donation history
      if (updateData.bloodGroup && updateData.rhType) {
        try {
          await bloodDonationService.updateBloodDonationHistory(selectedDonor.id, {
            appointmentId: selectedDonor.id,
            donationDate: new Date().toISOString(),
            bloodGroup: updateData.bloodGroup,
            rhType: updateData.rhType,
            doctorId: currentUser?.id || 4,
            notes: updateData.notes || "",
            isSuccess: true
          });

        } catch (bloodHistoryError) {
          console.warn("Failed to update blood donation history:", bloodHistoryError);
          // Don't throw error - continue with other operations
          toast.warning("⚠️ Thông tin sức khỏe đã được cập nhật, nhưng có lỗi khi lưu kết quả xét nghiệm máu.");
        }
      }

      // 3. Skip weight/height update for now due to API validation issues
      // TODO: Fix PUT /api/Information/{id} API validation requirements
      /*
      if (updateData.weight || updateData.height) {
        try {
          await bloodDonationService.updateUserInformation(selectedDonor.userId, {
            weight: updateData.weight,
            height: updateData.height
          });
        } catch (weightHeightError) {
          console.warn("Failed to update weight/height, but continuing:", weightHeightError);
          // Don't throw error - weight/height update is optional
        }
      }
      */

      // Call success callback to refresh data
      if (onSuccess) {
        onSuccess();
      }

      setShowUpdateModal(false);
      setSelectedDonor(null);
      toast.success("Cập nhật thông tin thành công!");
    } catch (error) {
      console.error("Failed to update donor information:", error);
      toast.error(" Có lỗi xảy ra khi cập nhật thông tin!");
    }
  };

  // Save status update
  const handleSaveStatusUpdate = async (selectedDonor, statusUpdateData, currentUser, onSuccess, setShowStatusModal, setSelectedDonor) => {
    if (!selectedDonor) return;

    try {
      const statusToSend = typeof statusUpdateData.status === 'string' ?
        parseInt(statusUpdateData.status) : statusUpdateData.status;

      const processToSend = typeof statusUpdateData.process === 'string' ?
        parseInt(statusUpdateData.process) : statusUpdateData.process;



      // 1. Update appointment status using PATCH /api/Appointment/{id}/status/{status}
      if (statusToSend && statusToSend !== selectedDonor.status) {
        await bloodDonationService.updateAppointmentStatus(
          selectedDonor.id,
          statusToSend,
          null, // Don't send notes with status update
          selectedDonor.userId
        );

      }

      // 2. Update appointment process using PATCH /api/Appointment/{id}/process/{process}
      if (processToSend && processToSend !== selectedDonor.process) {
        await bloodDonationService.updateAppointmentProcess(
          selectedDonor.id,
          processToSend,
          null, // Don't send notes with process update
          selectedDonor.userId
        );

      }

      // 3. Always update notes (even if empty to clear previous notes)
      try {
        await bloodDonationService.updateAppointmentNotes(
          selectedDonor.id,
          statusUpdateData.notes || ""
        );

      } catch (notesError) {
        console.error(' Failed to update notes:', notesError);
        // Don't throw error - continue with other operations
      }

      // 4. Call success callback to refresh data from server
      if (onSuccess) {
        onSuccess();
      }

      setShowStatusModal(false);
      setSelectedDonor(null);
      toast.success(" Cập nhật trạng thái thành công!");

      // Send notifications based on status - ONLY status update notifications
      if (statusToSend === 1) { // Rejected
        try {
          await NotificationService.createNotification({
            userId: selectedDonor.userId,
            type: "donation_status_update",
            title: " Cập nhật trạng thái hiến máu",
            message: "Cảm ơn bạn đã đăng ký hiến máu. Mặc dù lần này chưa phù hợp nhưng chúng tôi rất trân trọng tinh thần của bạn.",
            data: {
              appointmentId: selectedDonor.id,
              status: "Không chấp nhận",
              updateDate: new Date().toISOString().split("T")[0],
            },
          });
        } catch (notificationError) {
          console.warn("Could not send status notification:", notificationError);
        }
      } else if (statusToSend === 2) { // Approved
        try {
          await NotificationService.createNotification({
            userId: selectedDonor.userId,
            type: "donation_status_update",
            title: "Cập nhật trạng thái hiến máu",
            message: "Đăng ký hiến máu của bạn đã được chấp nhận. Vui lòng đến đúng giờ hẹn.",
            data: {
              appointmentId: selectedDonor.id,
              status: "Chấp nhận",
              updateDate: new Date().toISOString().split("T")[0],
            },
          });
        } catch (notificationError) {
          console.warn("Could not send status notification:", notificationError);
        }
      } else if (statusToSend === 3) { // Cancelled
        try {
          await NotificationService.createNotification({
            userId: selectedDonor.userId,
            type: "donation_status_update",
            title: " Cập nhật trạng thái hiến máu",
            message: "Lịch hẹn hiến máu của bạn đã được hủy. Bạn có thể đăng ký lại vào lần khác.",
            data: {
              appointmentId: selectedDonor.id,
              status: "Đã hủy",
              updateDate: new Date().toISOString().split("T")[0],
            },
          });
        } catch (notificationError) {
          console.warn("Could not send status notification:", notificationError);
        }
      }
    } catch (error) {
      console.error("Error updating donor status:", error);
      toast.error(" Có lỗi xảy ra khi cập nhật trạng thái!");
    }
  };

  return {
    handleSaveUpdate,
    handleSaveStatusUpdate,
  };
};
